/* eslint-disable no-unused-vars */
export enum QUESTION {
  Radio = "QuestionRadio",
  Ranking = "Ranking",
  Dropdown = "QuestionDropdown",
  Checkbox = "QuestionCheckbox",
  Input = "QuestionInput",
  Rating = "QuestionRating",
  Paragraph = "Paragraph",
}

export enum Survey {
  Upward = "SurveyIndexUpward",
  Self = "SurveyIndexSelf",
  Engagement = "SurveyIndexEngagement",
  _360Degree = "SurveyIndex360",
}

export enum SurveyStatus {
  Properties = "PROPERTIES",
  Questionnaire = "QUESTIONNAIRE",
  AddPairings = "ADD_PAIRINGS",
  Preview = "PREVIEW",
  Send = "SEND",
}

export enum Sort {
  Descending = "dsc",
  Ascending = "asc",
  None = "",
}

export enum Roles {
  ADMIN = "ADMIN",
  USER = "USER",
}

export enum Modules {
  Administration = "administration",
  UserDashboard = "user",
  Auth = "auth",
}

export enum SurveyResponse {
  Todo = "TODO",
  InProgress = "PROG",
  Decline = "DECL",
  Submit = "SUBM",
}
