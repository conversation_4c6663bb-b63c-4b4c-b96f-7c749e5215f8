// import {
//   osName,
//   browserName,
//   osVersion,
//   browserVersion,
//   isMobile,
// } from "mobile-device-detect";
import { UAParser } from 'ua-parser-js';

const parser = UAParser(navigator.userAgent);
const { os, browser } = parser;

const userAgent = {
  osClass: `os-${os?.name?.toLowerCase().split(" ").join("")}`
    .split(" ")
    .join("-"),
  browserClass: `browser-${browser?.name?.toLowerCase()}`.split(" ").join("-"),
  os: os?.name?.toLowerCase().split(" ").join(""),
  browser: browser?.name?.toLowerCase(),
  osVersion: os?.version,
  browserVersion: browser?.version,
  // isMobile: os?.type === "mobile",
};

export default userAgent;
