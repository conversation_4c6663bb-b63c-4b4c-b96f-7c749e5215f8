import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router';
import { But<PERSON> } from '@repo/ui/components/button';
import { Card, CardContent } from '@repo/ui/components/card';
import { Badge } from '@repo/ui/components/badge';
import { Skeleton } from '@repo/ui/components/skeleton';
import { Alert, AlertDescription } from '@repo/ui/components/alert';
import { Input } from '@repo/ui/components/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@repo/ui/components/select';
import { Checkbox } from '@repo/ui/components/checkbox';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@repo/ui/components/table';
import { ArrowLeft, Menu, Search } from 'lucide-react';
import { USER_ROUTES } from '@/app.routes';
import axiosInstance from '@/lib/axios';

interface User {
  id: number;
  first_name: string;
  last_name: string;
  emp_id: string;
  email: string;
  department: string;
  location: string;
  survey: string | number;
  response_id?: string;
  target: {
    first_name: string;
    last_name: string;
    emp_id: string;
  };
}

interface SurveyInfo {
  id: string;
  title: string;
  isPairsEditable: boolean;
  isDeclinable: boolean;
}

interface StatusCounts {
  TODO: number;
  PROG: number;
  SUBM: number;
  DECL: number;
}

const Pairings: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [users, setUsers] = useState<User[]>([]);
  const [surveyInfo, setSurveyInfo] = useState<SurveyInfo | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [status, setStatus] = useState('TODO');
  const [statusCounts, setStatusCounts] = useState<StatusCounts>({
    TODO: 0,
    PROG: 0,
    SUBM: 0,
    DECL: 0
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [filterValue, setFilterValue] = useState('All');
  const [selectedUsers, setSelectedUsers] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [showSidebar, setShowSidebar] = useState(false);

  useEffect(() => {
    if (id) {
      fetchSurveyInfo();
      fetchStatusCounts();
    }
  }, [id]);

  useEffect(() => {
    if (id) {
      fetchUsers();
    }
  }, [id, status, searchTerm]);

  const fetchSurveyInfo = async () => {
    try {
      const response = await axiosInstance.get(`/survey/index/${id}/`);
      setSurveyInfo(response.data);
    } catch (err) {
      console.error('Error fetching survey info:', err);
      setError('Failed to load survey information');
    }
  };

  const fetchStatusCounts = async () => {
    try {
      // Fetch counts for each status
      const statuses = ['TODO', 'PROG', 'SUBM', 'DECL'];
      const counts: StatusCounts = { TODO: 0, PROG: 0, SUBM: 0, DECL: 0 };

      for (const statusKey of statuses) {
        const response = await axiosInstance.get('/survey/pairing/', {
          params: {
            index_id: id,
            status: statusKey,
            page_size: 1,
            exclude_disabled: true
          }
        });
        counts[statusKey as keyof StatusCounts] = response.data.count_items || 0;
      }

      setStatusCounts(counts);
    } catch (err) {
      console.error('Error fetching status counts:', err);
    }
  };

  const fetchUsers = async () => {
    try {
      setIsLoading(true);
      const response = await axiosInstance.get('/survey/pairing/', {
        params: {
          index_id: id,
          status,
          page_size: 50,
          exclude_disabled: true,
          ...(searchTerm && { search: searchTerm })
        }
      });
      setUsers(response.data.results || []);
      setError(null);
    } catch (err) {
      console.error('Error fetching users:', err);
      setError('Failed to load pairings');
    } finally {
      setIsLoading(false);
    }
  };

  const handleStartSurvey = async (user: User) => {
    try {
      if (user.response_id && user.survey) {
        // Navigate directly if response already exists
        navigate(USER_ROUTES().dashboard.upwardReview.getTakeSurveyUrl(user.response_id, user.survey.toString()));
      } else {
        // Create new response
        const response = await axiosInstance.post('/survey/survey-response/', {
          survey: user.survey,
          pairing: user.id
        });
        navigate(USER_ROUTES().dashboard.upwardReview.getTakeSurveyUrl(response.data.id, response.data.survey));
      }
    } catch (err) {
      console.error('Error starting survey:', err);
      setError('Failed to start survey');
    }
  };

  const handleUserSelect = (userId: number) => {
    setSelectedUsers(prev =>
      prev.includes(userId)
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);
    if (checked) {
      setSelectedUsers(users.map(user => user.id));
    } else {
      setSelectedUsers([]);
    }
  };

  const getStatusLabel = (statusKey: string) => {
    const statusMap = {
      TODO: 'To-Do',
      PROG: 'In-Progress',
      SUBM: 'Completed',
      DECL: 'Declined'
    };
    return statusMap[statusKey as keyof typeof statusMap] || statusKey;
  };

  if (error) {
    return (
      <div className="p-6">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  // Sidebar Component
  const Sidebar = () => (
    <div className={`
      fixed inset-y-0 left-0 z-40 w-80 bg-white border-r transform transition-transform duration-300 ease-in-out
      ${showSidebar ? 'translate-x-0' : '-translate-x-full'}
      lg:translate-x-0 lg:static lg:inset-0
    `}>
      <div className="p-6 space-y-6">
        {/* Back Button */}
        <Button
          variant="ghost"
          onClick={() => navigate(USER_ROUTES().dashboard.surveyList)}
          className="w-full justify-start"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>

        {/* Survey Status */}
        <div>
          <h3 className="text-sm font-medium text-gray-500 mb-4">Survey Status</h3>
          <div className="space-y-2">
            {[
              { key: 'TODO', label: 'To-Do', count: statusCounts.TODO },
              { key: 'PROG', label: 'In-Progress', count: statusCounts.PROG },
              { key: 'SUBM', label: 'Completed', count: statusCounts.SUBM },
              ...(surveyInfo?.isDeclinable ? [{ key: 'DECL', label: 'Declined', count: statusCounts.DECL }] : [])
            ].map((item) => (
              <div
                key={item.key}
                className={`
                  flex justify-between items-center p-3 rounded cursor-pointer transition-colors
                  ${status === item.key
                    ? 'bg-blue-50 text-blue-700 border-l-2 border-blue-500'
                    : 'hover:bg-gray-50'
                  }
                `}
                onClick={() => {
                  setStatus(item.key);
                  setShowSidebar(false);
                }}
              >
                <span className="text-sm">{item.label}</span>
                <span className="font-semibold">
                  {isLoading ? <Skeleton className="h-4 w-6" /> : item.count}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Add Reviewee Button */}
        {surveyInfo?.isPairsEditable && (
          <div>
            <hr className="my-4" />
            <Button
              className="w-full"
              variant="outline"
              disabled={status === 'ADD'}
              onClick={() => setStatus('ADD')}
            >
              Add Reviewee
            </Button>
            <div className="mt-3 p-3 bg-yellow-50 rounded text-sm">
              <p>Please click <strong>Add Reviewee</strong> to add a new person to rate.</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <Sidebar />

      {/* Overlay for mobile */}
      {showSidebar && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden"
          onClick={() => setShowSidebar(false)}
        />
      )}

      {/* Main Content */}
      <div className="flex-1 flex flex-col lg:ml-0">
        {/* Header */}
        <div className="bg-white border-b px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="sm"
                className="lg:hidden mr-2"
                onClick={() => setShowSidebar(true)}
              >
                <Menu className="h-4 w-4" />
              </Button>
              <div>
                <h1 className="text-xl font-semibold text-orange-600 bg-orange-100 px-3 py-1 rounded">
                  {surveyInfo?.title || 'Survey'} - {getStatusLabel(status)}
                </h1>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white border-b px-6 py-4">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Select value={filterValue} onValueChange={setFilterValue}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="All">All</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex-1 max-w-md">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search for name, email or emp"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Content Area */}
        <div className="flex-1 p-6">
          {isLoading ? (
            <div className="space-y-4">
              {Array(5).fill(0).map((_, index) => (
                <Skeleton key={index} className="h-12 w-full" />
              ))}
            </div>
          ) : users.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500">
                There are no items to show. <button className="text-blue-600 hover:underline">Click here</button> add a new reviewee.
              </p>
            </div>
          ) : (
            <div className="bg-white rounded-lg border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">
                      <Checkbox
                        checked={selectAll}
                        onCheckedChange={handleSelectAll}
                      />
                    </TableHead>
                    <TableHead>No.</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Employee ID</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Department</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Action</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {users.map((user, index) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedUsers.includes(user.id)}
                          onCheckedChange={() => handleUserSelect(user.id)}
                        />
                      </TableCell>
                      <TableCell>{index + 1}.</TableCell>
                      <TableCell
                        className="cursor-pointer text-blue-600 hover:underline"
                        onClick={() => handleStartSurvey(user)}
                      >
                        {user.target.first_name} {user.target.last_name}
                      </TableCell>
                      <TableCell>{user.target.emp_id}</TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>{user.department}</TableCell>
                      <TableCell>{user.location}</TableCell>
                      <TableCell>
                        {(status === 'TODO' || status === 'PROG') && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleStartSurvey(user)}
                          >
                            {status === 'TODO' ? 'Start' : 'Continue'}
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Pairings;
