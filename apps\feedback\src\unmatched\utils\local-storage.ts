const getItem = (name: string) => {
  const val = window.localStorage.getItem(name);
  if (val) {
    return val.indexOf("{") < 0 ? val : JSON.parse(val);
  }
  return val;
};

const removeItem = (name: string) => {
  window.localStorage.removeItem(name);
};

const removeAll = () => {
  window.localStorage.clear();
};

const setItem = (name: string, data: any) => {
  const dataToSave = typeof data !== "object" ? data : JSON.stringify(data);
  window.localStorage.setItem(name, dataToSave);
};

const _localStorage = {
  getItem,
  removeItem,
  removeAll,
  setItem,
};

export default _localStorage;
