import { useState, useCallback } from 'react';
import { upwardReviewService, SurveyMeta, Section, Question, Analytics } from '@/services/upwardReviewService';

interface UseSurveyDataReturn {
  // State
  isLoading: boolean;
  isLoadingQuestions: boolean;
  isSaving: boolean;
  error: string | null;
  
  // Data
  meta: SurveyMeta | null;
  sections: Section[];
  currentSection: Section | null;
  questions: Question[];
  analytics: Analytics;
  faqs: any[];
  
  // Actions
  fetchSurveyData: (responseId: string, surveyId: string) => Promise<void>;
  loadQuestionsForSection: (sectionId: number) => Promise<void>;
  updateQuestionResponse: (questionId: number, response: any, feedback?: string) => Promise<void>;
  submitSurvey: (responseId: string) => Promise<void>;
  setCurrentSection: (section: Section) => void;
  setError: (error: string | null) => void;
  
  // Computed
  getCompletionPercentage: () => number;
}

export const useSurveyData = (): UseSurveyDataReturn => {
  // State
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingQuestions, setIsLoadingQuestions] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Data
  const [meta, setMeta] = useState<SurveyMeta | null>(null);
  const [sections, setSections] = useState<Section[]>([]);
  const [currentSection, setCurrentSection] = useState<Section | null>(null);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [analytics, setAnalytics] = useState<Analytics>({ total: 0, completed: 0, sections: [] });
  const [faqs, setFaqs] = useState<any[]>([]);

  // Fetch complete survey data
  const fetchSurveyData = useCallback(async (responseId: string, surveyId: string) => {
    try {
      setIsLoading(true);
      setError(null);

      // Store the response ID for API calls
      setCurrentResponseId(responseId);

      const surveyData = await upwardReviewService.getCompleteSurveyData(responseId, surveyId);

      setMeta(surveyData.meta);
      setSections(surveyData.sections);
      setCurrentSection(surveyData.sections[0] || null);
      setQuestions(surveyData.questions);
      setAnalytics(surveyData.analytics);

      // Fetch FAQs if we have an index ID
      if (surveyData.meta.indexId) {
        const faqData = await upwardReviewService.getFAQs(surveyData.meta.indexId);
        setFaqs(faqData);
      }

    } catch (err) {
      console.error('Error fetching survey data:', err);
      setError('Failed to load survey data');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Load questions for a specific section
  const loadQuestionsForSection = useCallback(async (_sectionId: number) => {
    try {
      setIsLoadingQuestions(true);
      
      // Filter questions for the current section
      // In the real implementation, this might involve a separate API call
      // For now, we'll filter from the existing questions
      // const sectionQuestions = questions.filter(q => q.categoryID === sectionId);
      
      // Update questions to show only the current section's questions
      // This is a simplified approach - in a real app, you might want to
      // fetch section-specific data from the API
      
      // Simulate a small delay for loading state
      await new Promise(resolve => setTimeout(resolve, 100));
      
    } catch (err) {
      console.error('Error loading questions for section:', err);
      setError('Failed to load questions for this section');
    } finally {
      setIsLoadingQuestions(false);
    }
  }, [questions]);

  // Store the current response ID for API calls
  const [currentResponseId, setCurrentResponseId] = useState<string | null>(null);

  // Update question response with optimistic updates
  const updateQuestionResponse = useCallback(async (
    questionId: number,
    response: any,
    feedback?: string
  ) => {
    if (!currentResponseId) return;

    // Store previous state for rollback
    const previousQuestions = questions;
    const previousAnalytics = analytics;

    // Find the question being updated
    const questionToUpdate = questions.find(q => q.id === questionId);
    const wasAnswered = questionToUpdate?.response !== undefined &&
                       questionToUpdate?.response !== null &&
                       questionToUpdate?.response !== '';
    const isNowAnswered = response !== undefined && response !== null && response !== '';

    // Get existing response ID if it exists
    const existingResponseId = questionToUpdate?.responseId;
    const existingCommentId = questionToUpdate?.commentId;

    // Optimistic update - update local state immediately
    setQuestions(prev => prev.map(q =>
      q.id === questionId ? { ...q, response, feedback } : q
    ));

    // Optimistically update analytics if answer status changed
    if (!wasAnswered && isNowAnswered) {
      // Question was just answered
      setAnalytics(prev => ({
        ...prev,
        completed: prev.completed + 1
      }));
    } else if (wasAnswered && !isNowAnswered) {
      // Question was unanswered
      setAnalytics(prev => ({
        ...prev,
        completed: Math.max(0, prev.completed - 1)
      }));
    }

    try {
      setIsSaving(true);

      // Update question response if response is provided
      if (response !== undefined && response !== null && response !== '') {
        await upwardReviewService.updateQuestionResponse(
          currentResponseId,
          questionId,
          response,
          existingResponseId
        );
      }

      // Update comment response if feedback is provided
      if (feedback !== undefined && feedback !== null && feedback !== '') {
        await upwardReviewService.updateCommentResponse(
          currentResponseId,
          questionId,
          feedback,
          existingCommentId
        );
      }

    } catch (err) {
      console.error('Error updating question:', err);

      // Rollback to previous state on error
      setQuestions(previousQuestions);
      setAnalytics(previousAnalytics);
      setError('Failed to save response. Please try again.');
    } finally {
      setIsSaving(false);
    }
  }, [currentResponseId, questions, analytics]);

  // Submit survey
  const submitSurvey = useCallback(async (responseId: string) => {
    try {
      setIsSaving(true);
      await upwardReviewService.submitSurvey(responseId);
    } catch (err) {
      console.error('Error submitting survey:', err);
      setError('Failed to submit survey');
      throw err; // Re-throw so the component can handle navigation
    } finally {
      setIsSaving(false);
    }
  }, []);

  // Get completion percentage
  const getCompletionPercentage = useCallback(() => {
    if (analytics.total === 0) return 0;
    return Math.round((analytics.completed / analytics.total) * 100);
  }, [analytics]);

  // Get questions for current section
  const getCurrentSectionQuestions = useCallback(() => {
    if (!currentSection) return questions;
    return questions.filter(q => q.categoryID === currentSection.id);
  }, [questions, currentSection]);

  return {
    // State
    isLoading,
    isLoadingQuestions,
    isSaving,
    error,
    
    // Data
    meta,
    sections,
    currentSection,
    questions: getCurrentSectionQuestions(),
    analytics,
    faqs,
    
    // Actions
    fetchSurveyData,
    loadQuestionsForSection,
    updateQuestionResponse,
    submitSurvey,
    setCurrentSection,
    setError,
    
    // Computed
    getCompletionPercentage
  };
};
