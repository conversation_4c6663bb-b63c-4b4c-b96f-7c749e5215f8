/* eslint-disable no-console */
// import env from "./env";

// const { isProd } = false;
const isProd = false;

const logger = {
  warning: (...args: any[]) => {
    if (isProd) return;
    console.log("-------------WARNING--------------");
    console.log(...args);
    console.log("----------------------------------");
  },
  info: (...args: any[]) => {
    if (isProd) return;
    console.log("-------------INFO-----------------");
    console.log(...args);
    console.log("----------------------------------");
  },
  error: (...args: any[]) => {
    if (isProd) return;
    console.log("---------ERROR------------------");
    console.log(...args);
    console.log("--------------------------------");
  },
  success: (...args: any[]) => {
    if (isProd) return;
    console.log("---------SUCCESS----------------");
    console.log(...args);
    console.log("--------------------------------");
  },
};

export default logger;
