export interface SurveyTypeConfig {
  displayName: string;
  bgColor: string;
  textColor: string;
  borderColor: string;
}

export const SURVEY_TYPE_CONFIGS: Record<string, SurveyTypeConfig> = {
  SurveyIndexEngagement: {
    displayName: 'Engagement Survey',
    bgColor: 'bg-blue-50',
    textColor: 'text-blue-700',
    borderColor: 'border-blue-200'
  },
  SurveyIndexUpward: {
    displayName: 'Upward Feedback',
    bgColor: 'bg-green-50',
    textColor: 'text-green-700',
    borderColor: 'border-green-200'
  },
  SurveyIndexSelf: {
    displayName: 'Self Evaluation',
    bgColor: 'bg-purple-50',
    textColor: 'text-purple-700',
    borderColor: 'border-purple-200'
  },
  SurveyIndex360: {
    displayName: '360 Degree Feedback',
    bgColor: 'bg-orange-50',
    textColor: 'text-orange-700',
    borderColor: 'border-orange-200'
  }
};

export const getSurveyTypeConfig = (resourcetype?: string): SurveyTypeConfig => {
  if (!resourcetype || !SURVEY_TYPE_CONFIGS[resourcetype]) {
    // Default config for unknown survey types
    return {
      displayName: 'Survey',
      bgColor: 'bg-gray-50',
      textColor: 'text-gray-700',
      borderColor: 'border-gray-200'
    };
  }
  
  return SURVEY_TYPE_CONFIGS[resourcetype];
};

export const getSurveyTypeName = (resourcetype?: string): string => {
  return getSurveyTypeConfig(resourcetype).displayName;
};
