import axiosInstance from '@/lib/axios';

export interface SurveyMeta {
  title: string;
  surveyFor: string;
  endDate: string;
  lastModified: string;
  canSubmit: boolean;
  nextTitle: string;
  buttonType: string;
  hideBack: boolean;
  indexId: string;
}

export interface Section {
  id: number;
  title: string;
  completed: boolean;
}

export interface Question {
  id: number;
  title: string;
  type: string;
  feedback?: string;
  hasFeedback: boolean;
  options?: any[];
  response?: any;
  responseId?: string;
  commentId?: string;
  categoryID?: number;
  isMandatory?: boolean;
  hasSwap?: boolean;
}

export interface Analytics {
  total: number;
  completed: number;
  sections: number[];
}

export interface SurveyData {
  meta: SurveyMeta;
  sections: Section[];
  questions: Question[];
  analytics: Analytics;
}

// Transform API response to match the expected format
const transformSurveyResponse = (responsesData: any, surveyData: any): SurveyData => {
  const { first_name, last_name } = responsesData?.pairing_detail?.target || {};

  // Extract sections from responsesData.survey_detail (similar to engagement survey)
  const surveyDetail = responsesData.survey_detail || {};
  const sections = surveyDetail.sections?.map((section: any) => ({
    id: section.id,
    title: section.name || section.title,
    completed: false // Will be calculated based on responses
  })) || [];

  // Create a map of responses by question ID
  const responseMap = new Map();
  if (responsesData.question_responses) {
    responsesData.question_responses.forEach((response: any) => {
      responseMap.set(response.question, response);
    });
  }

  // Create a map of comments by question ID
  const commentMap = new Map();
  if (responsesData.comment_responses) {
    responsesData.comment_responses.forEach((comment: any) => {
      commentMap.set(comment.question, comment);
    });
  }

  // Extract questions from sections.components (similar to engagement survey)
  const allQuestions: any[] = [];
  sections.forEach((section: any) => {
    if (section.components) {
      section.components.forEach((component: any) => {
        const response = responseMap.get(component.id);
        const comment = commentMap.get(component.id);

        allQuestions.push({
          id: component.id,
          title: component.label,
          type: component.resourcetype,
          categoryID: section.id,
          isMandatory: component.mandatory,
          hasFeedback: component.collect_feedback,
          hasSwap: component.is_reverse_scale,
          options: component.options || [],
          response: response?.value,
          responseId: response?.id,
          feedback: comment?.value,
          commentId: comment?.id
        });
      });
    }
  });

  // Calculate analytics
  const completedQuestions = allQuestions.filter((q: any) =>
    q.response !== undefined && q.response !== null && q.response !== ''
  ).length;

  const completedSections = sections.filter((section: any) => {
    const sectionQuestions = allQuestions.filter((q: any) => q.categoryID === section.id);
    return sectionQuestions.length > 0 && sectionQuestions.every((q: any) =>
      q.response !== undefined && q.response !== null && q.response !== ''
    );
  }).map((s: any) => s.id);

  return {
    meta: {
      title: responsesData.index?.title || 'Survey',
      surveyFor: first_name && last_name ? `${first_name} ${last_name}` : 'Unknown',
      endDate: responsesData.index?.deadline || '',
      lastModified: responsesData.updated_on || 'Just now',
      canSubmit: completedQuestions === allQuestions.length && allQuestions.length > 0,
      nextTitle: 'Next',
      buttonType: 'primary',
      hideBack: false,
      indexId: responsesData.index?.id || ''
    },
    sections: sections.map((section: any) => ({
      ...section,
      completed: completedSections.includes(section.id)
    })),
    questions: allQuestions,
    analytics: {
      total: allQuestions.length,
      completed: completedQuestions,
      sections: completedSections
    }
  };
};

export const upwardReviewService = {
  // Get survey response data
  getSurveyResponse: async (responseId: string): Promise<any> => {
    const response = await axiosInstance.get(`/survey/survey-response/${responseId}`);
    return response.data;
  },

  // Get survey structure data
  getSurveyData: async (surveyId: string): Promise<any> => {
    const response = await axiosInstance.get(`/survey/survey/${surveyId}`, {
      params: { survey: surveyId }
    });
    return response.data;
  },

  // Get complete survey data (combines response and structure)
  getCompleteSurveyData: async (responseId: string, surveyId: string): Promise<SurveyData> => {
    const [responsesData, surveyData] = await Promise.all([
      upwardReviewService.getSurveyResponse(responseId),
      upwardReviewService.getSurveyData(surveyId)
    ]);

    return transformSurveyResponse(responsesData, surveyData);
  },

  // Get FAQs for a survey
  getFAQs: async (surveyIndex: string): Promise<any[]> => {
    try {
      const response = await axiosInstance.get('/survey/faq/', {
        params: { survey_index: surveyIndex }
      });
      return response.data || [];
    } catch (error) {
      console.warn('Failed to fetch FAQs:', error);
      return [];
    }
  },

  // Update question response (similar to engagement survey)
  updateQuestionResponse: async (responseId: string, questionId: number, response: any, existingResponseId?: string): Promise<void> => {
    const payload = {
      survey_response: responseId,
      question: questionId,
      value: response,
    };

    if (existingResponseId) {
      // Update existing response
      await axiosInstance.patch(`/survey/question-response/${existingResponseId}/`, payload);
    } else {
      // Create new response
      await axiosInstance.post('/survey/question-response/', payload);
    }
  },

  // Update comment response
  updateCommentResponse: async (responseId: string, questionId: number, comment: string, existingCommentId?: string): Promise<void> => {
    const payload = {
      survey_response: responseId,
      question: questionId,
      value: comment,
    };

    if (existingCommentId) {
      // Update existing comment
      await axiosInstance.patch(`/survey/comment-response/${existingCommentId}/`, payload);
    } else {
      // Create new comment
      await axiosInstance.post('/survey/comment-response/', payload);
    }
  },

  // Submit survey
  submitSurvey: async (responseId: string): Promise<void> => {
    await axiosInstance.post(`/survey/survey-response/${responseId}/submit/`);
  }
};
